import 'dart:async';

import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:auto_route/auto_route.dart';
import 'package:share_plus/share_plus.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/ui/molecules/video_processing_dialog.dart';


Future<void> shareRecording(BuildContext context, String id) async {
  final uri = Uri.https('contents.melodyze.ai', '/share/r/$id');
  final box = context.findRenderObject() as RenderBox?;
  final origin = box != null ? box.localToGlobal(Offset.zero) & box.size : const Rect.fromLTWH(0, 0, 1, 1);

  // 3) Defer to next frame so we don’t present during build in release
  WidgetsBinding.instance.addPostFrameCallback((_) async {
    try {
      // Primary: share the URI
      await SharePlus.instance.share(
        ShareParams(
          uri: uri,
          sharePositionOrigin: origin,
        ),
      );
      logger.d("URL Share success");
    } catch (_) {
      // Fallback: some iOS builds prefer plain text
      await SharePlus.instance.share(
        ShareParams(
          text: uri.toString(),
          sharePositionOrigin: origin,
        ),
      );
      logger.d("Fallback: Text Share success");
    }
  });
}

class RecordingPopupMenu extends StatelessWidget {
  final RecordingModel recording;
  final void Function()? onStop;
  final bool isFromPlayer;
  final ValueChanged<RecordingModel>? onAction;

  const RecordingPopupMenu({
    super.key,
    required this.recording,
    this.onStop,
    this.isFromPlayer = false,
    this.onAction,
  });

  bool get isPublishedRecording => recording.isPublished;

  @override
  Widget build(BuildContext buildContext) {
    return PopupMenuButton<String>(
      icon: ShaderMask(
        shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
        child: const Icon(Icons.more_vert, size: 28, color: Colors.white),
      ),
      color: Colors.transparent,
      elevation: 0,
      padding: EdgeInsets.zero,
      itemBuilder: (BuildContext context) => [
        _CustomPopupMenuItem(
          buildContext: buildContext,
          recording: recording,
          isPublishedRecording: isPublishedRecording,
          onStop: onStop,
          isFromPlayer: isFromPlayer,
          onAction: onAction,
        ),
      ],
    );
  }
}

class _CustomPopupMenuItem extends PopupMenuEntry<String> {
  final BuildContext buildContext;
  final RecordingModel recording;
  final bool isPublishedRecording;
  final void Function()? onStop;
  final bool isFromPlayer;
  final ValueChanged<RecordingModel>? onAction;

  const _CustomPopupMenuItem({
    required this.buildContext,
    required this.recording,
    required this.isPublishedRecording,
    this.onStop,
    this.isFromPlayer = false,
    this.onAction,
  });

  @override
  double get height => 200;

  @override
  bool represents(String? value) => false;

  @override
  State<_CustomPopupMenuItem> createState() => _CustomPopupMenuItemState();
}

class _CustomPopupMenuItemState extends State<_CustomPopupMenuItem> {
  @override
  Widget build(BuildContext context) {
    return AppGradientContainer(
      gradient: AppGradients.gradientBlackTeal,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            leading: const Icon(
              Icons.check,
              color: AppColors.white,
            ),
            title: Text(
              widget.isPublishedRecording ? 'Move to drafts' : 'Make it publish',
              style: AppTextStyles.text16medium.copyWith(
                fontFamily: AppFonts.iceland,
                color: AppColors.white,
              ),
            ),
            onTap: () async {
              Navigator.pop(context);
              final result = await showYesNoDialog(
                context: widget.buildContext,
                title: widget.isPublishedRecording ? 'Move to drafts' : 'Make it publish',
              );
              if (result && widget.buildContext.mounted) {
                widget.onStop?.call();
                if (widget.isFromPlayer) {
                  if (widget.buildContext.router.canPop()) {
                    widget.buildContext.router.pop();
                  }
                }

                if (!widget.isPublishedRecording) {
                  unawaited(showVideoProcessingDialog(widget.buildContext));
                }

                DI().resolve<ProfileBloc>().add(ProfileTogglePublishEvent(
                      recording: widget.recording,
                      publish: !widget.isPublishedRecording,
                    ));
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
          ),
          ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            leading: Icon(
              Icons.share,
              color: widget.isPublishedRecording ? AppColors.white : AppColors.white.withValues(alpha: 0.5),
            ),
            title: Text(
              'Share',
              style: AppTextStyles.text16medium.copyWith(
                fontFamily: AppFonts.iceland,
                color: widget.isPublishedRecording ? AppColors.white : AppColors.white.withValues(alpha: 0.5),
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              if (widget.isPublishedRecording) {
                shareRecording(context, widget.recording.id);
              } else {
                DI().resolve<AppToast>().showToast('Please publish the recording first to share it.');
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
          ),
          ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            leading: const Icon(
              Icons.delete_forever,
              color: AppColors.white,
            ),
            title: Text(
              'Delete',
              style: AppTextStyles.text16medium.copyWith(
                fontFamily: AppFonts.iceland,
                color: AppColors.white,
              ),
            ),
            onTap: () async {
              Navigator.pop(context);
              final result = await showYesNoDialog(context: widget.buildContext, title: 'Delete recording');
              if (result && widget.buildContext.mounted) {
                widget.onStop?.call();
                DI().resolve<ProfileBloc>().add(DeleteRecordingEvent(widget.recording.id));
                if (widget.isFromPlayer) {
                  if (widget.buildContext.router.canPop()) {
                    widget.buildContext.router.pop();
                  }
                } else {
                  widget.onAction?.call(widget.recording);
                }
              }
            },
          ),
        ],
      ),
    );
  }
}
